'use client';

import type React from 'react';
import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { PermissionManager, type Role } from '@/lib/permissions';

interface AuthGuardProps {
  children: React.ReactNode;
  allowedRoles: Role[];
  requiredPermission?: string;
}

export function AuthGuard({ children, allowedRoles, requiredPermission }: AuthGuardProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const checkAuth = () => {
      const userRole = localStorage.getItem('userRole') as Role;

      if (!userRole) {
        router.push('/');
        return;
      }

      // Initialize permission manager with user role
      const permissionManager = PermissionManager.getInstance();
      permissionManager.setUserRole(userRole);

      // PREVENT super_admin from accessing employer routes
      if (userRole === 'super_admin' && pathname.startsWith('/employer')) {
        router.push('/admin/employers');
        return;
      }

      // PREVENT employer/sub_admin from accessing admin routes
      if ((userRole === 'employer' || userRole === 'sub_admin') && pathname.startsWith('/admin')) {
        router.push('/employer/dashboard');
        return;
      }

      // Check if user role is allowed - but only redirect if it's a critical mismatch
      if (!allowedRoles.includes(userRole)) {
        // Only redirect if user is on a completely wrong section
        // Allow users to stay on pages they can access even if not explicitly in allowedRoles
        const isOnWrongSection =
          (userRole === 'super_admin' && pathname.startsWith('/employer')) ||
          ((userRole === 'employer' || userRole === 'sub_admin') && pathname.startsWith('/admin'));

        if (isOnWrongSection) {
          redirectToRoleDashboard(userRole);
          return;
        }
      }

      // Additional permission check if specified
      if (requiredPermission && !permissionManager.hasPermission(requiredPermission as any)) {
        redirectToRoleDashboard(userRole);
        return;
      }

      setIsAuthorized(true);
      setIsLoading(false);
    };

    const redirectToRoleDashboard = (role: Role) => {
      switch (role) {
        case 'super_admin':
          router.push('/admin/employers');
          break;
        case 'sub_admin':
          router.push('/employer/dashboard'); // Sub-admin uses employer dashboard
          break;
        case 'employer':
          router.push('/employer/dashboard');
          break;
        default:
          router.push('/');
      }
    };

    checkAuth();
  }, [allowedRoles, requiredPermission, router, pathname]);

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
      </div>
    );
  }

  if (!isAuthorized) {
    return null;
  }

  return <>{children}</>;
}
