'use client';

import { useAuthStore } from '@/store/auth-store';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';

function ProtectedRoutesWrapper({
  children,
  isProtectedRoute,
}: {
  children: React.ReactNode;
  isProtectedRoute: boolean;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(true);
  const { authToken } = useAuthStore();

  useEffect(() => {
    console.log(
      'ProtectedRoutesWrapper - isProtectedRoute:',
      isProtectedRoute,
      'pathname:',
      pathname,
      'authToken:',
      !!authToken
    );

    if (!isProtectedRoute) {
      if (authToken) {
        const userRole = localStorage.getItem('userRole');

        // Add a small delay to avoid redirecting during Next.js navigation transitions
        // This prevents redirecting when the router temporarily goes through "/" during refresh
        const timeoutId = setTimeout(() => {
          console.log('Checking redirect after delay - userRole:', userRole, 'pathname:', pathname);

          // Only redirect if we're actually on an auth page (like login)
          // Don't redirect if we're already on a protected page
          if (pathname === '/' || pathname.startsWith('/auth') || pathname.startsWith('/login')) {
            console.log('Redirecting from auth page - userRole:', userRole);
            if (userRole === 'super_admin') {
              router.replace('/admin/employers');
            } else if (userRole === 'employer') {
              router.replace('/employer/dashboard');
            } else if (userRole === 'sub_admin') {
              router.replace('/employer/dashboard');
            } else {
              router.replace('/');
              localStorage.clear();
            }
          } else {
            // We're on a protected page but isProtectedRoute is false - this shouldn't happen
            console.warn('User is on protected page but isProtectedRoute is false:', pathname);
            setIsLoading(false);
          }
        }, 100); // Small delay to let navigation settle

        // Cleanup timeout if component unmounts or dependencies change
        return () => clearTimeout(timeoutId);
      } else {
        setIsLoading(false);
      }
    }
  }, [authToken, router, isProtectedRoute, pathname]);

  useEffect(() => {
    if (isProtectedRoute) {
      if (!authToken) {
        router.replace('/');
      } else {
        setIsLoading(false);
      }
    }
  }, [authToken, router, isProtectedRoute]);

  if (isLoading) {
    return <div className="min-h-screen animate-pulse bg-white"></div>;
  }

  return <>{children}</>;
}

export default ProtectedRoutesWrapper;
